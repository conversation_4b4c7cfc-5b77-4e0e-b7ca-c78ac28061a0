<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Face Slimming with face-api.js</title>
    <style>
        canvas {
            max-width: 100%;
            height: auto;
            border: 1px solid black;
        }
    </style>
</head>

<body>

    <h2>Face Slimming Demo</h2>

    <img id="inputImage"
        src="./face.jpg"
        crossorigin="anonymous" style="display:none" />

    <canvas id="canvas"></canvas>

    <script src="https://cdn.jsdelivr.net/npm/@vladmandic/face-api/dist/face-api.min.js"></script>
    <script>

        // Helper: Load models from CDN
        async function loadModels() {
            const MODEL_URL = 'https://cdn.jsdelivr.net/npm/@vladmandic/face-api/model/';
            await faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL);
            await faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL);
        }

        // Helper: Calculate distance between two points
        function distance(a, b) {
            return Math.sqrt((a.x - b.x) ** 2 + (a.y - b.y) ** 2);
        }

        // Helper: Linear interpolation
        function lerp(a, b, t) {
            return { x: a.x + (b.x - a.x) * t, y: a.y + (b.y - a.y) * t };
        }

        // Apply warp by moving jaw points inward for face slimming
        function getWarpedPoints(landmarks) {
            const points = landmarks.map(p => ({ x: p.x, y: p.y }));

            // Calculate face center as average of key central points
            const noseTip = points[30];
            const noseBridge = points[27];
            const chin = points[8];
            const center = {
                x: (noseTip.x + noseBridge.x + chin.x) / 3,
                y: (noseTip.y + noseBridge.y + chin.y) / 3
            };

            // More aggressive face slimming - move jaw points inward by 35%
            // Apply different intensities for different parts of the jaw
            for (let i = 0; i <= 16; i++) {
                let intensity;
                if (i >= 1 && i <= 3) {
                    intensity = 0.45; // Upper jaw area - more aggressive
                } else if (i >= 4 && i <= 6) {
                    intensity = 0.50; // Mid jaw area - most aggressive
                } else if (i >= 7 && i <= 9) {
                    intensity = 0.40; // Lower jaw/chin area
                } else if (i >= 10 && i <= 12) {
                    intensity = 0.50; // Mid jaw area (other side)
                } else if (i >= 13 && i <= 15) {
                    intensity = 0.45; // Upper jaw area (other side)
                } else {
                    intensity = 0.25; // Corner points - less aggressive
                }

                points[i] = lerp(points[i], center, intensity);
            }

            // Also slim the cheek area slightly by moving outer face points inward
            // Points around temples and cheeks
            const cheekPoints = [1, 2, 3, 13, 14, 15];
            for (const i of cheekPoints) {
                const cheekCenter = {
                    x: center.x,
                    y: points[i].y // Keep same height, just move horizontally
                };
                points[i] = lerp(points[i], cheekCenter, 0.25);
            }

            return points;
        }

        // Enhanced face slimming for weight loss effect - targets chin, cheeks, and jaw
        function applyFaceSlimming(ctx, img, landmarks) {
            // Calculate face center and key points
            const faceCenter = {
                x: (landmarks[27].x + landmarks[30].x) / 2, // nose area
                y: (landmarks[27].y + landmarks[30].y) / 2
            };

            // Create temporary canvas for the effect
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = img.width;
            tempCanvas.height = img.height;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.drawImage(img, 0, 0);

            // 1. Slim the cheeks (upper jaw area)
            const leftCheekCenter = {
                x: (landmarks[1].x + landmarks[2].x + landmarks[3].x) / 3,
                y: (landmarks[1].y + landmarks[2].y + landmarks[3].y) / 3
            };

            const rightCheekCenter = {
                x: (landmarks[13].x + landmarks[14].x + landmarks[15].x) / 3,
                y: (landmarks[13].y + landmarks[14].y + landmarks[15].y) / 3
            };

            // Apply VERY strong cheek slimming
            applyPinchEffect(tempCtx, leftCheekCenter.x, leftCheekCenter.y, 120, 0.8);
            applyPinchEffect(tempCtx, rightCheekCenter.x, rightCheekCenter.y, 120, 0.8);

            // 2. Slim the lower jaw/jowl area
            const leftJawCenter = {
                x: (landmarks[4].x + landmarks[5].x + landmarks[6].x) / 3,
                y: (landmarks[4].y + landmarks[5].y + landmarks[6].y) / 3
            };

            const rightJawCenter = {
                x: (landmarks[10].x + landmarks[11].x + landmarks[12].x) / 3,
                y: (landmarks[10].y + landmarks[11].y + landmarks[12].y) / 3
            };

            // Apply VERY strong jaw slimming
            applyPinchEffect(tempCtx, leftJawCenter.x, leftJawCenter.y, 110, 0.9);
            applyPinchEffect(tempCtx, rightJawCenter.x, rightJawCenter.y, 110, 0.9);

            // 3. Define and slim the chin area (double chin reduction)
            const chinCenter = {
                x: landmarks[8].x, // chin point
                y: landmarks[8].y + 40 // slightly below chin to target double chin area
            };

            // Apply strong chin slimming with vertical emphasis
            applyVerticalPinchEffect(tempCtx, chinCenter.x, chinCenter.y, 100, 0.7);

            // 4. Additional temple/upper cheek slimming
            const leftTempleCenter = {
                x: landmarks[0].x + 20,
                y: landmarks[19].y // around eyebrow level
            };

            const rightTempleCenter = {
                x: landmarks[16].x - 20,
                y: landmarks[24].y // around eyebrow level
            };

            applyPinchEffect(tempCtx, leftTempleCenter.x, leftTempleCenter.y, 70, 0.3);
            applyPinchEffect(tempCtx, rightTempleCenter.x, rightTempleCenter.y, 70, 0.3);

            // 5. Additional neck/lower chin area slimming
            const neckCenter = {
                x: landmarks[8].x,
                y: landmarks[8].y + 60 // further below chin for neck area
            };

            // Apply neck slimming
            applyVerticalPinchEffect(tempCtx, neckCenter.x, neckCenter.y, 100, 0.35);

            // 6. Subtle overall face narrowing (very light effect on outer edges)
            const leftOuterFace = {
                x: landmarks[0].x - 10,
                y: (landmarks[0].y + landmarks[8].y) / 2
            };

            const rightOuterFace = {
                x: landmarks[16].x + 10,
                y: (landmarks[16].y + landmarks[8].y) / 2
            };

            applyPinchEffect(tempCtx, leftOuterFace.x, leftOuterFace.y, 120, 0.25);
            applyPinchEffect(tempCtx, rightOuterFace.x, rightOuterFace.y, 120, 0.25);

            return tempCanvas;
        }

        // Pinch effect - pulls pixels toward center point
        function applyPinchEffect(ctx, centerX, centerY, radius, strength) {
            const imageData = ctx.getImageData(0, 0, ctx.canvas.width, ctx.canvas.height);
            const newImageData = ctx.createImageData(imageData.width, imageData.height);

            // Copy all pixels first
            for (let i = 0; i < imageData.data.length; i++) {
                newImageData.data[i] = imageData.data[i];
            }

            // Apply pinch effect
            for (let y = Math.max(0, centerY - radius); y < Math.min(imageData.height, centerY + radius); y++) {
                for (let x = Math.max(0, centerX - radius); x < Math.min(imageData.width, centerX + radius); x++) {
                    const dx = x - centerX;
                    const dy = y - centerY;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < radius && distance > 0) {
                        // Calculate pinch factor
                        const factor = Math.pow(1 - distance / radius, 2) * strength;

                        // Calculate source position (pull toward center)
                        const sourceX = Math.round(centerX + dx * (1 - factor));
                        const sourceY = Math.round(centerY + dy * (1 - factor));

                        // Copy pixel if source is valid
                        if (sourceX >= 0 && sourceX < imageData.width &&
                            sourceY >= 0 && sourceY < imageData.height) {

                            const sourceIndex = (sourceY * imageData.width + sourceX) * 4;
                            const targetIndex = (y * imageData.width + x) * 4;

                            newImageData.data[targetIndex] = imageData.data[sourceIndex];
                            newImageData.data[targetIndex + 1] = imageData.data[sourceIndex + 1];
                            newImageData.data[targetIndex + 2] = imageData.data[sourceIndex + 2];
                            newImageData.data[targetIndex + 3] = imageData.data[sourceIndex + 3];
                        }
                    }
                }
            }

            ctx.putImageData(newImageData, 0, 0);
        }

        // Vertical pinch effect - specifically for chin/double chin reduction
        function applyVerticalPinchEffect(ctx, centerX, centerY, radius, strength) {
            const imageData = ctx.getImageData(0, 0, ctx.canvas.width, ctx.canvas.height);
            const newImageData = ctx.createImageData(imageData.width, imageData.height);

            // Copy all pixels first
            for (let i = 0; i < imageData.data.length; i++) {
                newImageData.data[i] = imageData.data[i];
            }

            // Apply vertical pinch effect (stronger vertical compression)
            for (let y = Math.max(0, centerY - radius); y < Math.min(imageData.height, centerY + radius); y++) {
                for (let x = Math.max(0, centerX - radius); x < Math.min(imageData.width, centerX + radius); x++) {
                    const dx = x - centerX;
                    const dy = y - centerY;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < radius && distance > 0) {
                        // Calculate pinch factor with stronger vertical effect
                        const factor = Math.pow(1 - distance / radius, 2) * strength;

                        // Apply stronger vertical compression for chin slimming
                        const verticalFactor = factor * 1.5; // 50% stronger vertical effect
                        const horizontalFactor = factor * 0.7; // Slightly less horizontal effect

                        // Calculate source position
                        const sourceX = Math.round(centerX + dx * (1 - horizontalFactor));
                        const sourceY = Math.round(centerY + dy * (1 - verticalFactor));

                        // Copy pixel if source is valid
                        if (sourceX >= 0 && sourceX < imageData.width &&
                            sourceY >= 0 && sourceY < imageData.height) {

                            const sourceIndex = (sourceY * imageData.width + sourceX) * 4;
                            const targetIndex = (y * imageData.width + x) * 4;

                            newImageData.data[targetIndex] = imageData.data[sourceIndex];
                            newImageData.data[targetIndex + 1] = imageData.data[sourceIndex + 1];
                            newImageData.data[targetIndex + 2] = imageData.data[sourceIndex + 2];
                            newImageData.data[targetIndex + 3] = imageData.data[sourceIndex + 3];
                        }
                    }
                }
            }

            ctx.putImageData(newImageData, 0, 0);
        }



        async function main() {
            try {
                await loadModels();

                const img = document.getElementById('inputImage');
                const canvas = document.getElementById('canvas');

                // Wait for image to load before processing
                if (!img.complete) {
                    await new Promise((resolve, reject) => {
                        img.onload = resolve;
                        img.onerror = reject;
                    });
                }

                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;
                const ctx = canvas.getContext('2d');

                // Detect face and landmarks
                const detection = await faceapi.detectSingleFace(img).withFaceLandmarks();
                if (!detection) {
                    alert('No face detected');
                    return;
                }

                const landmarks = detection.landmarks.positions;

                // Debug: Draw landmarks to verify detection
                console.log('Detected landmarks:', landmarks.length);

                // Draw original image first
                ctx.drawImage(img, 0, 0);

                // Debug: Draw landmark points
                ctx.fillStyle = 'red';
                landmarks.forEach((point, i) => {
                    ctx.beginPath();
                    ctx.arc(point.x, point.y, 2, 0, 2 * Math.PI);
                    ctx.fill();

                    // Label key points
                    if ([0, 8, 16, 27, 30].includes(i)) {
                        ctx.fillStyle = 'white';
                        ctx.font = '12px Arial';
                        ctx.fillText(i.toString(), point.x + 5, point.y - 5);
                        ctx.fillStyle = 'red';
                    }
                });

                // Apply aggressive face slimming
                const slimmedCanvas = applyFaceSlimming(ctx, img, landmarks);

                // Clear canvas and draw the slimmed result
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(slimmedCanvas, 0, 0);
            } catch (error) {
                console.error('Error in face slimming:', error);
                alert('Error processing image: ' + error.message);
            }
        }

        // Wait for DOM to be ready before running
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', main);
        } else {
            main();
        }

    </script>

</body>

</html>