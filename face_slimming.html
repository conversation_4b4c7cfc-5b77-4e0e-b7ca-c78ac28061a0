<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Face Slimming with face-api.js</title>
    <style>
        canvas {
            max-width: 100%;
            height: auto;
            border: 1px solid black;
        }
    </style>
</head>

<body>

    <h2>Face Slimming Demo</h2>

    <img id="inputImage"
        src="./face.jpg"
        crossorigin="anonymous" style="display:none" />

    <canvas id="canvas"></canvas>

    <script src="https://cdn.jsdelivr.net/npm/@vladmandic/face-api/dist/face-api.min.js"></script>
    <script>

        // Helper: Load models from CDN
        async function loadModels() {
            const MODEL_URL = 'https://cdn.jsdelivr.net/npm/@vladmandic/face-api/model/';
            await faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL);
            await faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL);
        }

        // Helper: Calculate distance between two points
        function distance(a, b) {
            return Math.sqrt((a.x - b.x) ** 2 + (a.y - b.y) ** 2);
        }

        // Helper: Linear interpolation
        function lerp(a, b, t) {
            return { x: a.x + (b.x - a.x) * t, y: a.y + (b.y - a.y) * t };
        }

        // Apply warp by moving jaw points inward for face slimming
        function getWarpedPoints(landmarks) {
            const points = landmarks.map(p => ({ x: p.x, y: p.y }));

            // Calculate face center as average of key central points
            const noseTip = points[30];
            const noseBridge = points[27];
            const chin = points[8];
            const center = {
                x: (noseTip.x + noseBridge.x + chin.x) / 3,
                y: (noseTip.y + noseBridge.y + chin.y) / 3
            };

            // More aggressive face slimming - move jaw points inward by 35%
            // Apply different intensities for different parts of the jaw
            for (let i = 0; i <= 16; i++) {
                let intensity;
                if (i >= 1 && i <= 3) {
                    intensity = 0.45; // Upper jaw area - more aggressive
                } else if (i >= 4 && i <= 6) {
                    intensity = 0.50; // Mid jaw area - most aggressive
                } else if (i >= 7 && i <= 9) {
                    intensity = 0.40; // Lower jaw/chin area
                } else if (i >= 10 && i <= 12) {
                    intensity = 0.50; // Mid jaw area (other side)
                } else if (i >= 13 && i <= 15) {
                    intensity = 0.45; // Upper jaw area (other side)
                } else {
                    intensity = 0.25; // Corner points - less aggressive
                }

                points[i] = lerp(points[i], center, intensity);
            }

            // Also slim the cheek area slightly by moving outer face points inward
            // Points around temples and cheeks
            const cheekPoints = [1, 2, 3, 13, 14, 15];
            for (const i of cheekPoints) {
                const cheekCenter = {
                    x: center.x,
                    y: points[i].y // Keep same height, just move horizontally
                };
                points[i] = lerp(points[i], cheekCenter, 0.25);
            }

            return points;
        }

        // Simple pixel-based warping approach to avoid triangulation overlaps
        function applyFaceSlimming(ctx, img, landmarks, warpedPoints) {
            const imageData = ctx.getImageData(0, 0, ctx.canvas.width, ctx.canvas.height);
            const newImageData = ctx.createImageData(imageData.width, imageData.height);

            // Copy original image data
            for (let i = 0; i < imageData.data.length; i++) {
                newImageData.data[i] = imageData.data[i];
            }

            // Create displacement map for jaw area only
            const jawIndices = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16];

            // For each pixel in the image
            for (let y = 0; y < imageData.height; y++) {
                for (let x = 0; x < imageData.width; x++) {
                    let totalWeight = 0;
                    let displacementX = 0;
                    let displacementY = 0;

                    // Check influence from jaw points
                    for (const i of jawIndices) {
                        const original = landmarks[i];
                        const warped = warpedPoints[i];

                        const distance = Math.sqrt((x - original.x) ** 2 + (y - original.y) ** 2);
                        const maxInfluence = 100; // pixels

                        if (distance < maxInfluence) {
                            const weight = Math.pow(1 - (distance / maxInfluence), 2);
                            const dx = warped.x - original.x;
                            const dy = warped.y - original.y;

                            displacementX += dx * weight;
                            displacementY += dy * weight;
                            totalWeight += weight;
                        }
                    }

                    if (totalWeight > 0) {
                        displacementX /= totalWeight;
                        displacementY /= totalWeight;

                        // Calculate source pixel position
                        const sourceX = Math.round(x - displacementX);
                        const sourceY = Math.round(y - displacementY);

                        // Copy pixel if source is within bounds
                        if (sourceX >= 0 && sourceX < imageData.width &&
                            sourceY >= 0 && sourceY < imageData.height) {

                            const sourceIndex = (sourceY * imageData.width + sourceX) * 4;
                            const targetIndex = (y * imageData.width + x) * 4;

                            newImageData.data[targetIndex] = imageData.data[sourceIndex];         // R
                            newImageData.data[targetIndex + 1] = imageData.data[sourceIndex + 1]; // G
                            newImageData.data[targetIndex + 2] = imageData.data[sourceIndex + 2]; // B
                            newImageData.data[targetIndex + 3] = imageData.data[sourceIndex + 3]; // A
                        }
                    }
                }
            }

            return newImageData;
        }



        async function main() {
            try {
                await loadModels();

                const img = document.getElementById('inputImage');
                const canvas = document.getElementById('canvas');

                // Wait for image to load before processing
                if (!img.complete) {
                    await new Promise((resolve, reject) => {
                        img.onload = resolve;
                        img.onerror = reject;
                    });
                }

                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;
                const ctx = canvas.getContext('2d');

                // Detect face and landmarks
                const detection = await faceapi.detectSingleFace(img).withFaceLandmarks();
                if (!detection) {
                    alert('No face detected');
                    return;
                }

                const landmarks = detection.landmarks.positions;

                // Get warped points with jaw points moved inward
                const warpedPoints = getWarpedPoints(landmarks);

                // Draw the original image first
                ctx.drawImage(img, 0, 0);

                // Apply pixel-based face slimming (no overlapping triangles)
                const slimmedImageData = applyFaceSlimming(ctx, img, landmarks, warpedPoints);

                // Clear canvas and draw the slimmed result
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.putImageData(slimmedImageData, 0, 0);
            } catch (error) {
                console.error('Error in face slimming:', error);
                alert('Error processing image: ' + error.message);
            }
        }

        // Wait for DOM to be ready before running
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', main);
        } else {
            main();
        }

    </script>

</body>

</html>