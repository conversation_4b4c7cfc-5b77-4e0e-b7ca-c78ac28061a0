<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Face Slimming with face-api.js</title>
    <style>
        canvas {
            max-width: 100%;
            height: auto;
            border: 1px solid black;
        }
    </style>
</head>

<body>

    <h2>Face Slimming Demo</h2>

    <img id="inputImage"
        src="./face.jpg"
        crossorigin="anonymous" style="display:none" />

    <canvas id="canvas"></canvas>

    <script src="https://cdn.jsdelivr.net/npm/@vladmandic/face-api/dist/face-api.min.js"></script>
    <script>

        // Helper: Load models from CDN
        async function loadModels() {
            const MODEL_URL = 'https://cdn.jsdelivr.net/npm/@vladmandic/face-api/model/';
            await faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL);
            await faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL);
        }

        // Helper: Calculate distance between two points
        function distance(a, b) {
            return Math.sqrt((a.x - b.x) ** 2 + (a.y - b.y) ** 2);
        }

        // Helper: Linear interpolation
        function lerp(a, b, t) {
            return { x: a.x + (b.x - a.x) * t, y: a.y + (b.y - a.y) * t };
        }

        // Apply warp by moving jaw points inward for face slimming
        function getWarpedPoints(landmarks) {
            const points = landmarks.map(p => ({ x: p.x, y: p.y }));

            // Calculate face center as average of key central points
            const noseTip = points[30];
            const noseBridge = points[27];
            const chin = points[8];
            const center = {
                x: (noseTip.x + noseBridge.x + chin.x) / 3,
                y: (noseTip.y + noseBridge.y + chin.y) / 3
            };

            // More aggressive face slimming - move jaw points inward by 35%
            // Apply different intensities for different parts of the jaw
            for (let i = 0; i <= 16; i++) {
                let intensity;
                if (i >= 1 && i <= 3) {
                    intensity = 0.45; // Upper jaw area - more aggressive
                } else if (i >= 4 && i <= 6) {
                    intensity = 0.50; // Mid jaw area - most aggressive
                } else if (i >= 7 && i <= 9) {
                    intensity = 0.40; // Lower jaw/chin area
                } else if (i >= 10 && i <= 12) {
                    intensity = 0.50; // Mid jaw area (other side)
                } else if (i >= 13 && i <= 15) {
                    intensity = 0.45; // Upper jaw area (other side)
                } else {
                    intensity = 0.25; // Corner points - less aggressive
                }

                points[i] = lerp(points[i], center, intensity);
            }

            // Also slim the cheek area slightly by moving outer face points inward
            // Points around temples and cheeks
            const cheekPoints = [1, 2, 3, 13, 14, 15];
            for (const i of cheekPoints) {
                const cheekCenter = {
                    x: center.x,
                    y: points[i].y // Keep same height, just move horizontally
                };
                points[i] = lerp(points[i], cheekCenter, 0.25);
            }

            return points;
        }

        // Simplified triangulation focusing on key face areas to avoid overlaps
        const TRIANGLES = [
            // Jaw line - main slimming area
            [0, 1, 17], [1, 2, 17], [2, 3, 31], [3, 4, 31], [4, 5, 48],
            [5, 6, 48], [6, 7, 8], [7, 8, 9], [8, 9, 10], [9, 10, 11],
            [10, 11, 12], [11, 12, 54], [12, 13, 54], [13, 14, 35], [14, 15, 35],
            [15, 16, 26], [16, 26, 35],

            // Connect jaw to facial features
            [17, 18, 36], [18, 19, 37], [19, 20, 38], [20, 21, 39],
            [21, 22, 40], [22, 23, 42], [23, 24, 43], [24, 25, 44],
            [25, 26, 45], [26, 27, 46],

            // Eye areas (minimal triangulation)
            [36, 37, 38], [38, 39, 40], [40, 41, 36], [36, 38, 40],
            [42, 43, 44], [44, 45, 46], [46, 47, 42], [42, 44, 46],

            // Nose area
            [27, 28, 29], [29, 30, 31], [31, 32, 33], [33, 34, 35],
            [27, 29, 31], [31, 33, 35],

            // Mouth area (simplified)
            [48, 49, 50], [50, 51, 52], [52, 53, 54], [54, 55, 56],
            [56, 57, 58], [58, 59, 48], [48, 50, 52], [52, 54, 56],
            [56, 58, 48], [48, 52, 56]
        ];

        // Apply affine transform to warp triangles
        function warpTriangle(ctx, img, t1, t2) {
            // Calculate bounding rectangle for t1 and t2
            function boundingRect(points) {
                const xs = points.map(p => p.x);
                const ys = points.map(p => p.y);
                return {
                    x: Math.floor(Math.min(...xs)),
                    y: Math.floor(Math.min(...ys)),
                    w: Math.ceil(Math.max(...xs)) - Math.floor(Math.min(...xs)),
                    h: Math.ceil(Math.max(...ys)) - Math.floor(Math.min(...ys)),
                };
            }

            const r1 = boundingRect(t1);
            const r2 = boundingRect(t2);

            // Offset points relative to bounding rects
            const t1Offset = t1.map(p => ({ x: p.x - r1.x, y: p.y - r1.y }));
            const t2Offset = t2.map(p => ({ x: p.x - r2.x, y: p.y - r2.y }));

            // Create clipping path for destination triangle
            ctx.save();
            ctx.beginPath();
            ctx.moveTo(t2Offset[0].x + r2.x, t2Offset[0].y + r2.y);
            ctx.lineTo(t2Offset[1].x + r2.x, t2Offset[1].y + r2.y);
            ctx.lineTo(t2Offset[2].x + r2.x, t2Offset[2].y + r2.y);
            ctx.closePath();
            ctx.clip();

            // Compute affine transform matrix from t1Offset to t2Offset
            const matrix = calculateAffineTransform(t1Offset, t2Offset);

            // Draw the source triangle onto the destination canvas with transform
            ctx.setTransform(
                matrix.a, matrix.b,
                matrix.c, matrix.d,
                matrix.e + r2.x, matrix.f + r2.y
            );

            ctx.drawImage(img, r1.x, r1.y, r1.w, r1.h, 0, 0, r1.w, r1.h);

            ctx.restore();
        }

        // Calculate affine transform matrix between two triangles
        function calculateAffineTransform(src, dst) {
            // Solves linear equations to get 2x3 affine matrix
            // Based on https://math.stackexchange.com/questions/1461326/find-affine-transformation-matrix-from-3-points
            const x0 = src[0].x, y0 = src[0].y;
            const x1 = src[1].x, y1 = src[1].y;
            const x2 = src[2].x, y2 = src[2].y;

            const u0 = dst[0].x, v0 = dst[0].y;
            const u1 = dst[1].x, v1 = dst[1].y;
            const u2 = dst[2].x, v2 = dst[2].y;

            const denom = (x0 * (y1 - y2) + x1 * (y2 - y0) + x2 * (y0 - y1));

            // Check for degenerate triangle (avoid division by zero)
            if (Math.abs(denom) < 1e-10) {
                return { a: 1, b: 0, c: 0, d: 1, e: 0, f: 0 }; // Identity transform
            }

            const a = (u0 * (y1 - y2) + u1 * (y2 - y0) + u2 * (y0 - y1)) / denom;
            const b = (v0 * (y1 - y2) + v1 * (y2 - y0) + v2 * (y0 - y1)) / denom;
            const c = (u0 * (x2 - x1) + u1 * (x0 - x2) + u2 * (x1 - x0)) / denom;
            const d = (v0 * (x2 - x1) + v1 * (x0 - x2) + v2 * (x1 - x0)) / denom;
            const e = (u0 * (x1 * y2 - x2 * y1) + u1 * (x2 * y0 - x0 * y2) + u2 * (x0 * y1 - x1 * y0)) / denom;
            const f = (v0 * (x1 * y2 - x2 * y1) + v1 * (x2 * y0 - x0 * y2) + v2 * (x0 * y1 - x1 * y0)) / denom;

            return { a, b, c, d, e, f };
        }

        async function main() {
            try {
                await loadModels();

                const img = document.getElementById('inputImage');
                const canvas = document.getElementById('canvas');

                // Wait for image to load before processing
                if (!img.complete) {
                    await new Promise((resolve, reject) => {
                        img.onload = resolve;
                        img.onerror = reject;
                    });
                }

                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;
                const ctx = canvas.getContext('2d');

                // Detect face and landmarks
                const detection = await faceapi.detectSingleFace(img).withFaceLandmarks();
                if (!detection) {
                    alert('No face detected');
                    return;
                }

                const landmarks = detection.landmarks.positions;

                // Get warped points with jaw points moved inward
                const warpedPoints = getWarpedPoints(landmarks);

                // First, draw the original image as background
                ctx.drawImage(img, 0, 0);

                // Create a temporary canvas for the warped face
                const tempCanvas = document.createElement('canvas');
                tempCanvas.width = canvas.width;
                tempCanvas.height = canvas.height;
                const tempCtx = tempCanvas.getContext('2d');

                // Clear temp canvas
                tempCtx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);

                // Warp each triangle from original to warped points on temp canvas
                for (const tri of TRIANGLES) {
                    // Check if all triangle indices are valid
                    if (tri.every(i => i < landmarks.length && i < warpedPoints.length)) {
                        const t1 = tri.map(i => landmarks[i]);
                        const t2 = tri.map(i => warpedPoints[i]);
                        warpTriangle(tempCtx, img, t1, t2);
                    }
                }

                // Create a mask for the face area to blend smoothly
                const maskCanvas = document.createElement('canvas');
                maskCanvas.width = canvas.width;
                maskCanvas.height = canvas.height;
                const maskCtx = maskCanvas.getContext('2d');

                // Create face mask using the face outline
                maskCtx.fillStyle = 'white';
                maskCtx.beginPath();
                for (let i = 0; i <= 16; i++) {
                    const point = warpedPoints[i];
                    if (i === 0) {
                        maskCtx.moveTo(point.x, point.y);
                    } else {
                        maskCtx.lineTo(point.x, point.y);
                    }
                }
                maskCtx.closePath();
                maskCtx.fill();

                // Apply the warped face with the mask
                ctx.save();
                ctx.globalCompositeOperation = 'source-over';
                ctx.drawImage(tempCanvas, 0, 0);
                ctx.restore();
            } catch (error) {
                console.error('Error in face slimming:', error);
                alert('Error processing image: ' + error.message);
            }
        }

        // Wait for DOM to be ready before running
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', main);
        } else {
            main();
        }

    </script>

</body>

</html>