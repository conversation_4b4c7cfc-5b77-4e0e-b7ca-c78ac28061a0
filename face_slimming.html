<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Face Slimming with face-api.js</title>
    <style>
        canvas {
            max-width: 100%;
            height: auto;
            border: 1px solid black;
        }
    </style>
</head>

<body>

    <h2>Face Slimming Demo</h2>

    <img id="inputImage"
        src="./face.jpg"
        crossorigin="anonymous" style="display:none" />

    <canvas id="canvas"></canvas>

    <script src="https://cdn.jsdelivr.net/npm/@vladmandic/face-api/dist/face-api.min.js"></script>
    <script>

        // Helper: Load models from CDN
        async function loadModels() {
            const MODEL_URL = 'https://cdn.jsdelivr.net/npm/@vladmandic/face-api/model/';
            await faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL);
            await faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL);
        }

        // Helper: Calculate distance between two points
        function distance(a, b) {
            return Math.sqrt((a.x - b.x) ** 2 + (a.y - b.y) ** 2);
        }

        // Helper: Linear interpolation
        function lerp(a, b, t) {
            return { x: a.x + (b.x - a.x) * t, y: a.y + (b.y - a.y) * t };
        }

        // Apply warp by moving jaw points inward
        function getWarpedPoints(landmarks) {
            const points = landmarks.map(p => ({ x: p.x, y: p.y }));

            // Calculate face center roughly as midpoint between nose tip (30) and chin (8)
            const center = lerp(points[30], points[8], 0.5);

            // Move jaw points inward by 15%
            for (let i = 0; i <= 16; i++) {
                points[i] = lerp(points[i], center, 0.15);
            }

            return points;
        }

        // Triangulation setup (predefined triangles for 68 landmarks)
        // More complete triangulation covering the face properly
        const TRIANGLES = [
            // Jaw line triangles
            [0, 1, 36], [1, 2, 36], [2, 3, 31], [3, 4, 31], [4, 5, 48],
            [5, 6, 48], [6, 7, 57], [7, 8, 57], [8, 9, 56], [9, 10, 56],
            [10, 11, 55], [11, 12, 55], [12, 13, 54], [13, 14, 54], [14, 15, 35],
            [15, 16, 35], [16, 26, 35],

            // Right eye area
            [36, 37, 17], [37, 38, 18], [38, 39, 19], [39, 40, 20], [40, 41, 21],
            [41, 36, 17], [17, 18, 36], [18, 19, 37], [19, 20, 38], [20, 21, 39],
            [21, 22, 40], [22, 27, 41],

            // Left eye area
            [42, 43, 22], [43, 44, 23], [44, 45, 24], [45, 46, 25], [46, 47, 26],
            [47, 42, 22], [22, 23, 42], [23, 24, 43], [24, 25, 44], [25, 26, 45],
            [26, 27, 46],

            // Nose area
            [27, 28, 31], [28, 29, 31], [29, 30, 31], [30, 33, 31], [31, 32, 35],
            [32, 33, 35], [33, 34, 35], [34, 30, 35],

            // Mouth area
            [48, 49, 31], [49, 50, 32], [50, 51, 33], [51, 52, 34], [52, 53, 35],
            [53, 54, 35], [54, 55, 12], [55, 56, 11], [56, 57, 10], [57, 58, 9],
            [58, 59, 8], [59, 48, 7], [48, 60, 49], [49, 61, 50], [50, 62, 51],
            [51, 63, 52], [52, 64, 53], [53, 65, 54], [54, 66, 55], [55, 67, 56],
            [56, 67, 57], [57, 67, 58], [58, 67, 59], [59, 60, 48]
        ];

        // Apply affine transform to warp triangles
        function warpTriangle(ctx, img, t1, t2) {
            // Calculate bounding rectangle for t1 and t2
            function boundingRect(points) {
                const xs = points.map(p => p.x);
                const ys = points.map(p => p.y);
                return {
                    x: Math.floor(Math.min(...xs)),
                    y: Math.floor(Math.min(...ys)),
                    w: Math.ceil(Math.max(...xs)) - Math.floor(Math.min(...xs)),
                    h: Math.ceil(Math.max(...ys)) - Math.floor(Math.min(...ys)),
                };
            }

            const r1 = boundingRect(t1);
            const r2 = boundingRect(t2);

            // Offset points relative to bounding rects
            const t1Offset = t1.map(p => ({ x: p.x - r1.x, y: p.y - r1.y }));
            const t2Offset = t2.map(p => ({ x: p.x - r2.x, y: p.y - r2.y }));

            // Create clipping path for destination triangle
            ctx.save();
            ctx.beginPath();
            ctx.moveTo(t2Offset[0].x + r2.x, t2Offset[0].y + r2.y);
            ctx.lineTo(t2Offset[1].x + r2.x, t2Offset[1].y + r2.y);
            ctx.lineTo(t2Offset[2].x + r2.x, t2Offset[2].y + r2.y);
            ctx.closePath();
            ctx.clip();

            // Compute affine transform matrix from t1Offset to t2Offset
            const matrix = calculateAffineTransform(t1Offset, t2Offset);

            // Draw the source triangle onto the destination canvas with transform
            ctx.setTransform(
                matrix.a, matrix.b,
                matrix.c, matrix.d,
                matrix.e + r2.x, matrix.f + r2.y
            );

            ctx.drawImage(img, r1.x, r1.y, r1.w, r1.h, 0, 0, r1.w, r1.h);

            ctx.restore();
        }

        // Calculate affine transform matrix between two triangles
        function calculateAffineTransform(src, dst) {
            // Solves linear equations to get 2x3 affine matrix
            // Based on https://math.stackexchange.com/questions/1461326/find-affine-transformation-matrix-from-3-points
            const x0 = src[0].x, y0 = src[0].y;
            const x1 = src[1].x, y1 = src[1].y;
            const x2 = src[2].x, y2 = src[2].y;

            const u0 = dst[0].x, v0 = dst[0].y;
            const u1 = dst[1].x, v1 = dst[1].y;
            const u2 = dst[2].x, v2 = dst[2].y;

            const denom = (x0 * (y1 - y2) + x1 * (y2 - y0) + x2 * (y0 - y1));

            // Check for degenerate triangle (avoid division by zero)
            if (Math.abs(denom) < 1e-10) {
                return { a: 1, b: 0, c: 0, d: 1, e: 0, f: 0 }; // Identity transform
            }

            const a = (u0 * (y1 - y2) + u1 * (y2 - y0) + u2 * (y0 - y1)) / denom;
            const b = (v0 * (y1 - y2) + v1 * (y2 - y0) + v2 * (y0 - y1)) / denom;
            const c = (u0 * (x2 - x1) + u1 * (x0 - x2) + u2 * (x1 - x0)) / denom;
            const d = (v0 * (x2 - x1) + v1 * (x0 - x2) + v2 * (x1 - x0)) / denom;
            const e = (u0 * (x1 * y2 - x2 * y1) + u1 * (x2 * y0 - x0 * y2) + u2 * (x0 * y1 - x1 * y0)) / denom;
            const f = (v0 * (x1 * y2 - x2 * y1) + v1 * (x2 * y0 - x0 * y2) + v2 * (x0 * y1 - x1 * y0)) / denom;

            return { a, b, c, d, e, f };
        }

        async function main() {
            try {
                await loadModels();

                const img = document.getElementById('inputImage');
                const canvas = document.getElementById('canvas');

                // Wait for image to load before processing
                if (!img.complete) {
                    await new Promise((resolve, reject) => {
                        img.onload = resolve;
                        img.onerror = reject;
                    });
                }

                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;
                const ctx = canvas.getContext('2d');

                // Detect face and landmarks
                const detection = await faceapi.detectSingleFace(img).withFaceLandmarks();
                if (!detection) {
                    alert('No face detected');
                    return;
                }

                const landmarks = detection.landmarks.positions;

                // Get warped points with jaw points moved inward
                const warpedPoints = getWarpedPoints(landmarks);

                // Clear canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Warp each triangle from original to warped points
                for (const tri of TRIANGLES) {
                    // Check if all triangle indices are valid
                    if (tri.every(i => i < landmarks.length && i < warpedPoints.length)) {
                        const t1 = tri.map(i => landmarks[i]);
                        const t2 = tri.map(i => warpedPoints[i]);
                        warpTriangle(ctx, img, t1, t2);
                    }
                }

                // Draw non-triangular parts (like nose, eyes) unchanged (optional)
                // For simplicity, just draw the original image below warped layer
                // so uncovered areas show original
                ctx.globalCompositeOperation = 'destination-over';
                ctx.drawImage(img, 0, 0);
            } catch (error) {
                console.error('Error in face slimming:', error);
                alert('Error processing image: ' + error.message);
            }
        }

        // Wait for DOM to be ready before running
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', main);
        } else {
            main();
        }

    </script>

</body>

</html>